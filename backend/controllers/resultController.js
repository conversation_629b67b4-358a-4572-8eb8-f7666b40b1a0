const Result = require('../models/Result');
const Question = require('../models/Question');
const ExamRetry = require('../models/ExamRetry');

exports.submitExam = async (req, res) => {
    const { examId, answers } = req.body;
    try {
        console.log('Submitting exam:', { examId, answersCount: answers.length });

        // First, get ALL questions for this exam to calculate total marks correctly
        const allQuestions = await Question.find({ examId });
        console.log(`Found ${allQuestions.length} questions for exam ${examId}`);

        // Calculate total possible marks from ALL questions in the exam
        const totalMarks = allQuestions.reduce((sum, question) => {
            return sum + (Number(question.marks) || 0);
        }, 0);

        console.log(`Total possible marks for exam: ${totalMarks}`);

        let score = 0;
        const processedAnswers = [];

        // Process each submitted answer
        for (let answer of answers) {
            const question = allQuestions.find(q => q._id.toString() === answer.questionId);
            if (question) {
                const questionMarks = Number(question.marks) || 0;
                const isCorrect = Number(answer.selectedAnswer) === Number(question.correctAnswer);
                const marksAwarded = isCorrect ? questionMarks : 0;
                score += marksAwarded;

                processedAnswers.push({
                    questionId: answer.questionId,
                    selectedAnswer: Number(answer.selectedAnswer),
                    isCorrect: isCorrect,
                    marksAwarded: marksAwarded
                });

                console.log(`Question ${question._id}: ${isCorrect ? 'Correct' : 'Incorrect'} - ${marksAwarded}/${questionMarks} marks`);
            } else {
                console.warn(`Question not found in exam: ${answer.questionId}`);
                // Still add the answer but with 0 marks
                processedAnswers.push({
                    questionId: answer.questionId,
                    selectedAnswer: Number(answer.selectedAnswer),
                    isCorrect: false,
                    marksAwarded: 0
                });
            }
        }

        // Calculate percentage (round to 2 decimal places)
        const percentage = totalMarks > 0 ? Math.round((score / totalMarks) * 10000) / 100 : 0;

        // Determine if student passed (60% or higher)
        const isPassed = percentage >= 60;

        // Check if this is a retry attempt
        const existingResults = await Result.find({ studentId: req.user.id, examId });
        const attemptNumber = existingResults.length + 1;

        // If this is a retry, mark the retry as used
        if (attemptNumber > 1) {
            await ExamRetry.findOneAndUpdate(
                { studentId: req.user.id, examId, isUsed: false },
                { isUsed: true, usedAt: new Date() }
            );
        }

        console.log(`Final calculation: ${score}/${totalMarks} marks = ${percentage}% (${isPassed ? 'PASSED' : 'FAILED'}) - Attempt ${attemptNumber}`);

        const result = new Result({
            studentId: req.user.id,
            examId,
            answers: processedAnswers,
            score,
            totalMarks,
            percentage,
            isPassed,
            attemptNumber,
        });

        await result.save();

        // Populate the result before sending response
        const populatedResult = await Result.findById(result._id)
            .populate('studentId', 'username email')
            .populate('examId', 'title description duration');

        console.log('Result saved successfully:', {
            score: populatedResult.score,
            totalMarks: populatedResult.totalMarks,
            percentage: populatedResult.percentage
        });

        res.json(populatedResult);
    } catch (err) {
        console.error('Error submitting exam:', err);
        res.status(500).json({ msg: 'Server error while submitting exam' });
    }
};

exports.getResultsByStudent = async (req, res) => {
    try {
        const results = await Result.find({ studentId: req.params.studentId }).populate('examId', 'title');
        res.json(results);
    } catch (err) {
        res.status(500).send('Server error');
    }
};

exports.getResultsByExam = async (req, res) => {
    try {
        const results = await Result.find({ examId: req.params.examId }).populate('studentId', 'username');
        res.json(results);
    } catch (err) {
        res.status(500).send('Server error');
    }
};

exports.getMyResults = async (req, res) => {
    try {
        const results = await Result.find({ studentId: req.user.id })
            .populate('studentId', 'username email')
            .populate('examId', 'title description duration')
            .sort({ submittedAt: -1 });
        res.json(results);
    } catch (err) {
        console.error('Error getting my results:', err);
        res.status(500).send('Server error');
    }
};

exports.getAllResults = async (req, res) => {
    try {
        const results = await Result.find()
            .populate('studentId', 'username email')
            .populate('examId', 'title description duration')
            .sort({ submittedAt: -1 });
        res.json(results);
    } catch (err) {
        console.error('Error getting all results:', err);
        res.status(500).send('Server error');
    }
};