const Result = require('../models/Result');
const Question = require('../models/Question');

exports.submitExam = async (req, res) => {
    const { examId, answers } = req.body;
    try {
        console.log('Submitting exam:', { examId, answersCount: answers.length });

        let score = 0;
        let totalMarks = 0;
        const processedAnswers = [];

        // Process each answer and calculate scores
        for (let answer of answers) {
            const question = await Question.findById(answer.questionId);
            if (question) {
                // Ensure marks is a valid number
                const questionMarks = Number(question.marks) || 0;
                totalMarks += questionMarks;

                const isCorrect = Number(answer.selectedAnswer) === Number(question.correctAnswer);
                const marksAwarded = isCorrect ? questionMarks : 0;
                score += marksAwarded;

                processedAnswers.push({
                    questionId: answer.questionId,
                    selectedAnswer: Number(answer.selectedAnswer),
                    isCorrect: isCorrect,
                    marksAwarded: marksAwarded
                });

                console.log(`Question ${question._id}: ${isCorrect ? 'Correct' : 'Incorrect'} - ${marksAwarded}/${questionMarks} marks`);
            } else {
                console.warn(`Question not found: ${answer.questionId}`);
            }
        }

        // Calculate percentage (round to 2 decimal places)
        const percentage = totalMarks > 0 ? Math.round((score / totalMarks) * 10000) / 100 : 0;

        console.log(`Final calculation: ${score}/${totalMarks} marks = ${percentage}%`);

        const result = new Result({
            studentId: req.user.id,
            examId,
            answers: processedAnswers,
            score,
            totalMarks,
            percentage,
        });

        await result.save();

        // Populate the result before sending response
        const populatedResult = await Result.findById(result._id)
            .populate('studentId', 'username email')
            .populate('examId', 'title description duration');

        console.log('Result saved successfully:', {
            score: populatedResult.score,
            totalMarks: populatedResult.totalMarks,
            percentage: populatedResult.percentage
        });

        res.json(populatedResult);
    } catch (err) {
        console.error('Error submitting exam:', err);
        res.status(500).json({ msg: 'Server error while submitting exam' });
    }
};

exports.getResultsByStudent = async (req, res) => {
    try {
        const results = await Result.find({ studentId: req.params.studentId }).populate('examId', 'title');
        res.json(results);
    } catch (err) {
        res.status(500).send('Server error');
    }
};

exports.getResultsByExam = async (req, res) => {
    try {
        const results = await Result.find({ examId: req.params.examId }).populate('studentId', 'username');
        res.json(results);
    } catch (err) {
        res.status(500).send('Server error');
    }
};

exports.getMyResults = async (req, res) => {
    try {
        const results = await Result.find({ studentId: req.user.id })
            .populate('studentId', 'username email')
            .populate('examId', 'title description duration')
            .sort({ submittedAt: -1 });
        res.json(results);
    } catch (err) {
        console.error('Error getting my results:', err);
        res.status(500).send('Server error');
    }
};

exports.getAllResults = async (req, res) => {
    try {
        const results = await Result.find()
            .populate('studentId', 'username email')
            .populate('examId', 'title description duration')
            .sort({ submittedAt: -1 });
        res.json(results);
    } catch (err) {
        console.error('Error getting all results:', err);
        res.status(500).send('Server error');
    }
};