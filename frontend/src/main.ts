import { bootstrapApplication } from '@angular/platform-browser';
import { provideRouter } from '@angular/router';
import { HttpClientModule, provideHttpClient } from '@angular/common/http';
import { BrowserAnimationsModule, provideAnimations } from '@angular/platform-browser/animations';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule } from '@angular/forms';
import { AppComponent } from './app/app.component';
import { AdminDashboardComponent } from './app/pages/admin-dashboard/admin-dashboard.component';
import { LoginComponent } from './app/components/login/login.component';
import { RegisterComponent } from './app/components/register/register.component';
import { ExamListComponent } from './app/components/exam-list/exam-list.component';
import { ExamTakingComponent } from './app/components/exam-taking/exam-taking.component';
import { ResultViewComponent } from './app/components/result-view/result-view.component';
import { ExamManageComponent } from './app/components/exam-manage/exam-manage.component';
import { QuestionManageComponent } from './app/components/question-manage/question-manage.component';
import { ResultManageComponent } from './app/components/result-manage/result-manage.component';
import { StudentDashboardComponent } from './app/pages/student-dashboard/student-dashboard.component';
import { routes } from './app/app-routing.module';
import { AuthService } from './app/services/auth.service';
import { ExamService } from './app/services/exam.service';
import { QuestionService } from './app/services/question.service';
import { ResultService } from './app/services/result.service';
import { AuthGuard } from './app/guards/auth.guard';

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideHttpClient(),
    provideAnimations(),
    { provide: MatToolbarModule, useValue: MatToolbarModule },
    { provide: MatCardModule, useValue: MatCardModule },
    { provide: MatFormFieldModule, useValue: MatFormFieldModule },
    { provide: MatInputModule, useValue: MatInputModule },
    { provide: MatButtonModule, useValue: MatButtonModule },
    { provide: MatListModule, useValue: MatListModule },
    { provide: MatSelectModule, useValue: MatSelectModule },
    { provide: MatTableModule, useValue: MatTableModule },
    { provide: MatIconModule, useValue: MatIconModule },
    { provide: FlexLayoutModule, useValue: FlexLayoutModule },
    { provide: FormsModule, useValue: FormsModule },
    AuthService,
    ExamService,
    QuestionService,
    ResultService,
    AuthGuard
  ]
}).catch(err => console.error(err));
