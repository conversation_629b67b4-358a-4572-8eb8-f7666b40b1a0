import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Exam } from '../models/exam.model';

@Injectable({
  providedIn: 'root',
})
export class ExamService {
  private apiUrl = 'http://localhost:5000/api/exams';
  private token = localStorage.getItem('token');

  constructor(private http: HttpClient) {}

  getExams(): Observable<Exam[]> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.get<Exam[]>(this.apiUrl, { headers });
  }

  createExam(exam: { title: string; description: string; duration: number }): Observable<Exam> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.post<Exam>(this.apiUrl, exam, { headers });
  }

  updateExam(id: string, exam: Exam): Observable<Exam> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.put<Exam>(`${this.apiUrl}/${id}`, exam, { headers });
  }

  deleteExam(id: string): Observable<void> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.delete<void>(`${this.apiUrl}/${id}`, { headers });
  }
}