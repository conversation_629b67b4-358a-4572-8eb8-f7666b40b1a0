import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Result } from '../models/result.model';

@Injectable({
  providedIn: 'root',
})
export class ResultService {
  private apiUrl = 'http://localhost:5000/api/results';
  private token = localStorage.getItem('token');

  constructor(private http: HttpClient) {}

  getAllResults(): Observable<Result[]> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.get<Result[]>(this.apiUrl, { headers });
  }

  submitResult(data: { examId: string; answers: { questionId: string; selectedAnswer: number }[] }): Observable<Result> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.post<Result>(this.apiUrl, data, { headers });
  }
}