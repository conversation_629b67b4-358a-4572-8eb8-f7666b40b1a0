import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Question } from '../models/question.model';

@Injectable({
  providedIn: 'root',
})
export class QuestionService {
  private apiUrl = 'http://localhost:5000/api/questions';
  private token = localStorage.getItem('token');

  constructor(private http: HttpClient) {}

  getQuestionsByExam(examId: string): Observable<Question[]> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.get<Question[]>(`${this.apiUrl}/exam/${examId}`, { headers });
  }

  createQuestion(question: { examId: string; text: string; options: string[]; correctAnswer: number; marks: number }): Observable<Question> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.post<Question>(this.apiUrl, question, { headers });
  }

  updateQuestion(id: string, question: Question): Observable<Question> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.put<Question>(`${this.apiUrl}/${id}`, question, { headers });
  }

  deleteQuestion(id: string): Observable<void> {
    const headers = new HttpHeaders().set('x-auth-token', this.token || '');
    return this.http.delete<void>(`${this.apiUrl}/${id}`, { headers });
  }
}
