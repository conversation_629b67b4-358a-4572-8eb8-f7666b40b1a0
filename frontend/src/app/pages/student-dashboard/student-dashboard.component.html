<app-exam-list
  *ngIf="!selectedExam && !result"
  [exams]="exams"
  [takenExamIds]="takenExamIds"
  [loading]="loadingExam"
  (takeExam)="onTakeExam($event)">
</app-exam-list>

<app-exam-taking
  *ngIf="selectedExam && questions.length"
  [exam]="selectedExam"
  [questions]="questions"
  [answers]="answers"
  [loading]="loadingExam"
  (submitExam)="onSubmitExam($event)"
  (cancelExam)="onCancelExam()">
</app-exam-taking>

<app-result-view
  *ngIf="result"
  [result]="result"
  [percentage]="percentage"
  (back)="onBackFromResult()">
</app-result-view>
