/* Global Styles */
.student-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Roboto', sans-serif;
}

/* Header Styles */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-icon {
  font-size: 2rem;
  color: #fff;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: #fff;
}

.brand-subtitle {
  font-size: 0.875rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notification-btn, .user-btn {
  color: white;
}

/* Menu Styles */
.notification-menu, .user-menu {
  min-width: 320px;
  max-height: 400px;
  overflow-y: auto;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.notification-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: #f5f5f5;
}

.notification-item.unread {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.notification-message {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.notification-time {
  font-size: 0.75rem;
  color: #999;
}

.notification-type-icon {
  font-size: 1.25rem;
}

.notification-type-icon.success {
  color: #4caf50;
}

.notification-type-icon.warning {
  color: #ff9800;
}

.notification-type-icon.info {
  color: #2196f3;
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: #666;
}

.no-notifications mat-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #ccc;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
}

.user-avatar {
  font-size: 2.5rem;
  color: #666;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  font-size: 1rem;
}

.user-email {
  font-size: 0.875rem;
  color: #666;
}

/* Main Content */
.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.dashboard-tabs {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Tab Content */
.tab-content {
  padding: 2rem;
  min-height: 600px;
}

.tab-header {
  text-align: center;
  margin-bottom: 2rem;
}

.tab-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.tab-description {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-card mat-card-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem !important;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 16px;
  font-size: 1.5rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Stat Card Variants */
.stat-card.total-exams .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.total-exams .stat-value {
  color: #667eea;
}

.stat-card.completed-exams .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.completed-exams .stat-value {
  color: #4facfe;
}

.stat-card.average-score .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-card.average-score .stat-value {
  color: #43e97b;
}

.stat-card.completion-rate .stat-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-card.completion-rate .stat-value {
  color: #fa709a;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.quick-actions-card, .recent-results-card {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
}

.card-avatar.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-avatar.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-avatar.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-avatar.accent {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Exam Quick List */
.exam-quick-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.exam-quick-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.exam-quick-item:hover {
  border-color: #667eea;
  background-color: #f8fafc;
}

.exam-info {
  flex: 1;
}

.exam-title {
  font-weight: 600;
  font-size: 1rem;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.exam-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.exam-duration {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.exam-duration mat-icon {
  font-size: 1rem;
}

/* Results Quick List */
.results-quick-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-quick-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.result-quick-item:hover {
  border-color: #4facfe;
  background-color: #f8fafc;
}

.result-info {
  flex: 1;
}

.result-exam {
  font-weight: 600;
  font-size: 1rem;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.result-date {
  font-size: 0.875rem;
  color: #64748b;
}

.result-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.score-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.score-grade {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.8);
}

.score-details {
  font-size: 0.75rem;
  font-weight: 500;
  color: #64748b;
  margin-top: 0.25rem;
}

.score-marks {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  margin-top: 0.25rem;
}

.result-score.success .score-value {
  color: #10b981;
}

.result-score.warning .score-value {
  color: #f59e0b;
}

.result-score.danger .score-value {
  color: #ef4444;
}

.view-all-link {
  text-align: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

/* Search Section */
.search-section {
  margin-bottom: 2rem;
}

.search-field {
  width: 100%;
  max-width: 500px;
}

/* Exam Categories */
.exam-categories {
  margin-bottom: 2rem;
}

.category-chips {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Exams Grid */
.exams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.exam-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.exam-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.exam-card.completed {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
}

.exam-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
}

.exam-avatar.available {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.exam-avatar.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.exam-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.exam-creator {
  font-size: 0.875rem;
  color: #64748b;
}

.exam-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.exam-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.detail-item mat-icon {
  font-size: 1rem;
  color: #94a3b8;
}

.exam-result {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
}

.result-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.exam-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Results Tab */
.results-summary {
  margin-bottom: 2rem;
}

.summary-card {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.summary-stat {
  text-align: center;
}

.summary-stat .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.summary-stat .stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Results List */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.result-card {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.result-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
  font-weight: 700;
}

.result-avatar.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.result-avatar.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.result-avatar.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.grade-text {
  font-size: 0.875rem;
  font-weight: 700;
}

.result-exam-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.result-date {
  font-size: 0.875rem;
  color: #64748b;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.score-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.score-display {
  text-align: center;
  padding: 1rem;
  border-radius: 12px;
  min-width: 120px;
}

.score-display.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
  border: 2px solid rgba(16, 185, 129, 0.2);
}

.score-display.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
  border: 2px solid rgba(245, 158, 11, 0.2);
}

.score-display.danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  border: 2px solid rgba(239, 68, 68, 0.2);
}

.score-percentage {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.score-display.success .score-percentage {
  color: #10b981;
}

.score-display.warning .score-percentage {
  color: #f59e0b;
}

.score-display.danger .score-percentage {
  color: #ef4444;
}

.score-grade {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 600;
}

.result-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.result-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.result-stat mat-icon {
  font-size: 1rem;
  color: #94a3b8;
}

.score-progress {
  margin-top: 1rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

/* Profile Tab */
.profile-content {
  max-width: 600px;
  margin: 0 auto;
}

.profile-card {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-avatar {
  font-size: 3rem;
  color: #667eea;
}

.profile-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.profile-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.profile-stat mat-icon {
  font-size: 1.5rem;
  color: #667eea;
}

.stat-info {
  flex: 1;
}

.profile-stat .stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.profile-stat .stat-label {
  font-size: 0.875rem;
  color: #64748b;
}

/* Overlays */
.exam-overlay, .result-overlay, .loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: white;
  text-align: center;
}

.loading-content p {
  font-size: 1.125rem;
  font-weight: 500;
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: #64748b;
}

.empty-icon {
  margin-bottom: 1rem;
}

.empty-icon mat-icon {
  font-size: 4rem;
  color: #cbd5e1;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.5rem 0;
}

.empty-description {
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
  max-width: 400px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: #64748b;
}

.loading-state p {
  margin-top: 1rem;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .summary-stats {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 0.5rem;
  }

  .brand-title {
    font-size: 1.25rem;
  }

  .brand-subtitle {
    display: none;
  }

  .dashboard-main {
    padding: 1rem 0.5rem;
  }

  .tab-content {
    padding: 1rem;
  }

  .tab-title {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .exams-grid {
    grid-template-columns: 1fr;
  }

  .exam-quick-item, .result-quick-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .score-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .result-details {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .brand-section {
    gap: 0.5rem;
  }

  .brand-icon {
    font-size: 1.5rem;
  }

  .brand-title {
    font-size: 1rem;
  }

  .header-actions {
    gap: 0.25rem;
  }

  .notification-menu, .user-menu {
    min-width: 280px;
  }

  .tab-title {
    font-size: 1.25rem;
    flex-direction: column;
    gap: 0.25rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .exam-actions {
    flex-direction: column;
  }

  .profile-stats {
    gap: 0.75rem;
  }

  .profile-stat {
    padding: 0.75rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Material Design Overrides */
.mat-mdc-tab-group {
  --mdc-tab-indicator-active-indicator-color: #667eea;
  --mdc-secondary-navigation-tab-with-icon-and-label-text-label-text-color: #64748b;
  --mdc-secondary-navigation-tab-active-label-text-color: #667eea;
}

.mat-mdc-progress-bar .mdc-linear-progress__bar-inner {
  border-top-width: 6px;
}

.mat-mdc-card {
  --mdc-elevated-card-container-elevation: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.mat-mdc-button.mat-primary {
  --mdc-text-button-label-text-color: #667eea;
}

.mat-mdc-raised-button.mat-primary {
  --mdc-filled-button-container-color: #667eea;
}
