import { Component, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatRadioModule } from '@angular/material/radio';
import { ExamListComponent } from '../../components/exam-list/exam-list.component';
import { ExamTakingComponent } from '../../components/exam-taking/exam-taking.component';
import { ResultViewComponent } from '../../components/result-view/result-view.component';
import { ExamService } from '../../services/exam.service';
import { QuestionService } from '../../services/question.service';
import { ResultService } from '../../services/result.service';
import { Exam } from '../../models/exam.model';
import { Question } from '../../models/question.model';
import { Result, Answer } from '../../models/result.model';

@Component({
  selector: 'app-student-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatListModule,
    MatTableModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatRadioModule,
    FormsModule,
    ExamListComponent,
    ExamTakingComponent,
    ResultViewComponent
  ],
templateUrl: './student-dashboard.component.html',
  styleUrl: './student-dashboard.component.css'
})
export class StudentDashboardComponent implements OnInit {
  exams: Exam[] = [];
  takenExamIds: string[] = [];
  selectedExam: Exam | null = null;
  questions: Question[] = [];
  answers: number[] = [];
  result: Result | null = null;
  percentage: number | null = null;
  loadingExam = false;

  constructor(
    private examService: ExamService,
    private questionService: QuestionService,
    private resultService: ResultService
  ) {}

  ngOnInit(): void {
    this.loadExams();
    this.loadTakenExams();
  }

  loadExams(): void {
    this.examService.getExams().subscribe((data) => {
      this.exams = data;
    });
  }

  loadTakenExams(): void {
    // This should be replaced with a real API call to get student's taken exams
    // For now, just empty (simulate no exams taken)
    this.takenExamIds = [];
  }

  canTakeExam(exam: Exam): boolean {
    return !this.takenExamIds.includes(exam._id);
  }

  onTakeExam(exam: Exam): void {
    this.selectedExam = exam;
    this.loadingExam = true;
    this.questionService.getQuestionsByExam(exam._id).subscribe((questions) => {
      this.questions = questions;
      this.answers = Array(questions.length).fill(null);
      this.loadingExam = false;
    });
  }

  onSubmitExam(): void {
    if (!this.selectedExam) return;
    const answers: Answer[] = this.questions.map((q, i) => ({
      questionId: q._id,
      selectedAnswer: this.answers[i]
    }));
    this.loadingExam = true;
    this.resultService.submitResult({
      examId: this.selectedExam._id,
      answers
    }).subscribe((result) => {
      this.result = result;
      this.percentage = (result.score / (this.questions.reduce((sum, q) => sum + q.marks, 0))) * 100;
      this.takenExamIds.push(this.selectedExam!._id);
      this.selectedExam = null;
      this.questions = [];
      this.answers = [];
      this.loadingExam = false;
    }, () => { this.loadingExam = false; });
  }


  onBackFromResult(): void {
    this.result = null;
    this.percentage = null;
  }

  onCancelExam(): void {
    this.selectedExam = null;
    this.questions = [];
    this.answers = [];
  }
}
