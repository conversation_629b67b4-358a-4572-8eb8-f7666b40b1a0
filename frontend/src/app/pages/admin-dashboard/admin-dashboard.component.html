<div class="dashboard-container">
  <!-- Header Section -->
  <header class="dashboard-header">
    <div class="header-content">
      <div class="header-left">
        <mat-icon class="header-icon">admin_panel_settings</mat-icon>
        <div class="header-text">
          <h1 class="header-title">Admin Dashboard</h1>
          <p class="header-subtitle">Manage exams, questions, and view results</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-icon-button class="notification-btn" [matMenuTriggerFor]="notificationMenu">
          <mat-icon [matBadge]="3" matBadgeColor="warn">notifications</mat-icon>
        </button>
        <mat-menu #notificationMenu="matMenu">
          <button mat-menu-item>
            <mat-icon>info</mat-icon>
            <span>New student registered</span>
          </button>
          <button mat-menu-item>
            <mat-icon>assignment</mat-icon>
            <span>Exam completed</span>
          </button>
          <button mat-menu-item>
            <mat-icon>warning</mat-icon>
            <span>System maintenance</span>
          </button>
        </mat-menu>
        <button mat-icon-button [matMenuTriggerFor]="userMenu">
          <mat-icon>account_circle</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
          <button mat-menu-item>
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item>
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item>
            <mat-icon>logout</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </header>

  <!-- Stats Cards -->
  <section class="stats-section">
    <div class="stats-grid">
      <mat-card class="stat-card animate-scale-in">
        <div class="stat-content">
          <div class="stat-icon primary">
            <mat-icon>quiz</mat-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">{{ exams.length }}</h3>
            <p class="stat-label">Total Exams</p>
          </div>
        </div>
      </mat-card>

      <mat-card class="stat-card animate-scale-in">
        <div class="stat-content">
          <div class="stat-icon success">
            <mat-icon>help_outline</mat-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">{{ questions.length }}</h3>
            <p class="stat-label">Questions</p>
          </div>
        </div>
      </mat-card>

      <mat-card class="stat-card animate-scale-in">
        <div class="stat-content">
          <div class="stat-icon warning">
            <mat-icon>people</mat-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">{{ users.length }}</h3>
            <p class="stat-label">Total Users</p>
          </div>
        </div>
      </mat-card>

      <mat-card class="stat-card animate-scale-in">
        <div class="stat-content">
          <div class="stat-icon error">
            <mat-icon>assessment</mat-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">{{ results.length }}</h3>
            <p class="stat-label">Submissions</p>
          </div>
        </div>
      </mat-card>
    </div>
  </section>

  <!-- Main Content -->
  <div class="content-grid">
    <!-- Exam Management -->
    <mat-card class="management-card exam-card animate-fade-in">
      <mat-card-header class="card-header">
        <div class="header-icon-wrapper primary">
          <mat-icon>quiz</mat-icon>
        </div>
        <mat-card-title>Exam Management</mat-card-title>
        <mat-card-subtitle>Create and manage exams</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content class="card-content">
        <!-- Create Exam Form -->
        <div class="form-section">
          <h3 class="section-title">Create New Exam</h3>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Exam Title</mat-label>
              <input matInput [(ngModel)]="newExam.title" placeholder="Enter exam title">
              <mat-icon matPrefix>title</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Duration (minutes)</mat-label>
              <input matInput type="number" [(ngModel)]="newExam.duration" placeholder="60">
              <mat-icon matPrefix>timer</mat-icon>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Description</mat-label>
            <textarea matInput [(ngModel)]="newExam.description" placeholder="Enter exam description" rows="3"></textarea>
            <mat-icon matPrefix>description</mat-icon>
          </mat-form-field>

          <button
            mat-raised-button
            class="btn-gradient-primary create-btn"
            (click)="createExam()"
            [disabled]="!newExam.title || !newExam.description || !newExam.duration">
            <mat-icon>add</mat-icon>
            Create Exam
          </button>
        </div>

        <!-- Exams List -->
        <div class="list-section">
          <h3 class="section-title">Existing Exams</h3>
          @if (exams.length > 0) {
            <div class="exam-list">
              @for (exam of exams; track exam._id) {
                <div class="exam-item">
                  <div class="exam-info">
                    <h4 class="exam-title">{{ exam.title }}</h4>
                    <p class="exam-description">{{ exam.description }}</p>
                    <div class="exam-meta">
                      <span class="meta-item">
                        <mat-icon>timer</mat-icon>
                        {{ exam.duration }} min
                      </span>
                      <span class="meta-item">
                        <mat-icon>date_range</mat-icon>
                        {{ exam.createdAt | date:'short' }}
                      </span>
                    </div>
                  </div>
                  <div class="exam-actions">
                    <button mat-icon-button color="primary" (click)="startEditingExam(exam)" matTooltip="Edit exam">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="accent" (click)="loadQuestions(exam._id)" matTooltip="Manage questions">
                      <mat-icon>help_outline</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="deleteExam(exam._id)" matTooltip="Delete exam">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              }
            </div>
          } @else {
            <div class="empty-state">
              <mat-icon>quiz</mat-icon>
              <h4>No exams created yet</h4>
              <p>Create your first exam to get started</p>
            </div>
          }
        </div>

        <!-- Edit Exam Form -->
        @if (editingExam) {
          <div class="edit-section">
            <h3 class="section-title">Edit Exam</h3>
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Title</mat-label>
                <input matInput [(ngModel)]="editingExam.title">
                <mat-icon matPrefix>title</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Duration (minutes)</mat-label>
                <input matInput type="number" [(ngModel)]="editingExam.duration">
                <mat-icon matPrefix>timer</mat-icon>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput [(ngModel)]="editingExam.description" rows="3"></textarea>
              <mat-icon matPrefix>description</mat-icon>
            </mat-form-field>

            <div class="action-buttons">
              <button mat-raised-button class="btn-gradient-primary" (click)="updateExam()">
                <mat-icon>save</mat-icon>
                Save Changes
              </button>
              <button mat-button (click)="cancelEditing()">
                <mat-icon>cancel</mat-icon>
                Cancel
              </button>
            </div>
          </div>
        }
      </mat-card-content>
    </mat-card>

    <!-- Question Management -->
    <div class="card" fxFlex="30%">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Question Management</mat-card-title>
          <mat-select placeholder="Select Exam" (selectionChange)="loadQuestions($event.value)" [value]="selectedExamId">
            @for (exam of exams; track exam._id) {
              <mat-option [value]="exam._id">{{ exam.title }}</mat-option>
            }
          </mat-select>
        </mat-card-header>
        <mat-card-content>
          <mat-form-field>
            <input matInput placeholder="Question Text" [(ngModel)]="newQuestion.text">
          </mat-form-field>
          @for (option of newQuestion.options; track $index; let i = $index) {
            <mat-form-field>
              <input matInput placeholder="Option {{i + 1}}" [(ngModel)]="newQuestion.options[i]">
            </mat-form-field>
          }
          <mat-form-field>
            <mat-select placeholder="Correct Answer" [(ngModel)]="newQuestion.correctAnswer">
            @for (option of newQuestion.options; track $index; let i = $index) {
              <mat-option [value]="i">{{ option }}</mat-option>
            }
            </mat-select>
          </mat-form-field>
          <mat-form-field>
            <input matInput type="number" placeholder="Marks" [(ngModel)]="newQuestion.marks">
          </mat-form-field>
          <button mat-raised-button color="primary" (click)="createQuestion()" [disabled]="isCreateQuestionDisabled()">Create Question</button>
          <mat-list>
          @for (question of questions; track question._id) {
            <mat-list-item>
              <h4 matLine>{{ question.text }}</h4>
              <p matLine>Options: {{ question.options.join(', ') }}</p>
              <button mat-icon-button color="warn" (click)="deleteQuestion(question._id)"><mat-icon>delete</mat-icon></button>
              <button mat-icon-button color="accent" (click)="startEditingQuestion(question)"><mat-icon>edit</mat-icon></button>
            </mat-list-item>
          }
          </mat-list>
          @if (editingQuestion) {
            <div>
              <mat-form-field><input matInput placeholder="Text" [(ngModel)]="editingQuestion.text"></mat-form-field>
              @for (option of editingQuestion.options; track $index; let i = $index) {
                <mat-form-field>
                  <input matInput placeholder="Option {{i + 1}}" [(ngModel)]="editingQuestion.options[i]">
                </mat-form-field>
              }
              <mat-form-field>
                <mat-select placeholder="Correct Answer" [(ngModel)]="editingQuestion.correctAnswer">
                  @for (option of editingQuestion.options; track $index; let i = $index) {
                    <mat-option [value]="i">{{ option }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <mat-form-field><input matInput type="number" placeholder="Marks" [(ngModel)]="editingQuestion.marks"></mat-form-field>
              <button mat-raised-button color="primary" (click)="updateQuestion()">Save</button>
              <button mat-button (click)="cancelEditing()">Cancel</button>
            </div>
          }
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Result Management -->
    <div class="card" fxFlex="30%">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Result Management</mat-card-title>
          <mat-form-field>
            <input matInput placeholder="Search by Student or Exam" [(ngModel)]="searchTerm" (input)="onSearchChange()">
          </mat-form-field>
        </mat-card-header>
        <mat-card-content>
          <mat-table [dataSource]="filteredResults.length ? filteredResults : results" class="mat-elevation-z8">
            <ng-container matColumnDef="student">
              <mat-header-cell *matHeaderCellDef> Student </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.studentId.username}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="exam">
              <mat-header-cell *matHeaderCellDef> Exam </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.examId.title}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="score">
              <mat-header-cell *matHeaderCellDef> Score </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.score}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="submittedAt">
              <mat-header-cell *matHeaderCellDef> Submitted At </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{ result.submittedAt ? (result.submittedAt) : '' }} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="answers">
              <mat-header-cell *matHeaderCellDef> Answers </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.answers.length}} submitted </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="['student', 'exam', 'score', 'submittedAt', 'answers']"></mat-header-row>
            <mat-row *matRowDef="let row; columns: ['student', 'exam', 'score', 'submittedAt', 'answers'];"></mat-row>
          </mat-table>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
