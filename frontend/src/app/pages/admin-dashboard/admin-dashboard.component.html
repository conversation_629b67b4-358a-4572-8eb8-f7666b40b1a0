<div class="dashboard-container" fxLayout="column" fxLayoutGap="20px">
  <mat-toolbar color="primary">
    <mat-toolbar-row>
      <span>Admin Dashboard</span>
    </mat-toolbar-row>
  </mat-toolbar>

  <div class="content" fxLayout="row wrap" fxLayoutGap="20px">
    <!-- Exam Management -->
    <div class="card" fxFlex="30%">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Exam Management</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <mat-form-field>
            <input matInput placeholder="Exam Title" [(ngModel)]="newExam.title">
          </mat-form-field>
          <mat-form-field>
            <input matInput placeholder="Description" [(ngModel)]="newExam.description">
          </mat-form-field>
          <mat-form-field>
            <input matInput type="number" placeholder="Duration (min)" [(ngModel)]="newExam.duration">
          </mat-form-field>
          <button mat-raised-button color="primary" (click)="createExam()" [disabled]="!newExam.title || !newExam.description || !newExam.duration">Create Exam</button>
          <mat-list>
            @for (exam of exams; track exam._id) {
              <mat-list-item>
                <h4 matLine>{{ exam.title }}</h4>
                <p matLine>{{ exam.description }}</p>
                <button mat-icon-button color="warn" (click)="deleteExam(exam._id)"><mat-icon>delete</mat-icon></button>
                <button mat-icon-button color="accent" (click)="startEditingExam(exam)"><mat-icon>edit</mat-icon></button>
              </mat-list-item>
            }
          </mat-list>
          @if (editingExam) {
            <div>
              <mat-form-field><input matInput placeholder="Title" [(ngModel)]="editingExam.title"></mat-form-field>
              <mat-form-field><input matInput placeholder="Description" [(ngModel)]="editingExam.description"></mat-form-field>
              <mat-form-field><input matInput type="number" placeholder="Duration" [(ngModel)]="editingExam.duration"></mat-form-field>
              <button mat-raised-button color="primary" (click)="updateExam()">Save</button>
              <button mat-button (click)="cancelEditing()">Cancel</button>
            </div>
          }
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Question Management -->
    <div class="card" fxFlex="30%">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Question Management</mat-card-title>
          <mat-select placeholder="Select Exam" (selectionChange)="loadQuestions($event.value)" [value]="selectedExamId">
            @for (exam of exams; track exam._id) {
              <mat-option [value]="exam._id">{{ exam.title }}</mat-option>
            }
          </mat-select>
        </mat-card-header>
        <mat-card-content>
          <mat-form-field>
            <input matInput placeholder="Question Text" [(ngModel)]="newQuestion.text">
          </mat-form-field>
          @for (option of newQuestion.options; track $index; let i = $index) {
            <mat-form-field>
              <input matInput placeholder="Option {{i + 1}}" [(ngModel)]="newQuestion.options[i]">
            </mat-form-field>
          }
          <mat-form-field>
            <mat-select placeholder="Correct Answer" [(ngModel)]="newQuestion.correctAnswer">
            @for (option of newQuestion.options; track $index; let i = $index) {
              <mat-option [value]="i">{{ option }}</mat-option>
            }
            </mat-select>
          </mat-form-field>
          <mat-form-field>
            <input matInput type="number" placeholder="Marks" [(ngModel)]="newQuestion.marks">
          </mat-form-field>
          <button mat-raised-button color="primary" (click)="createQuestion()" [disabled]="isCreateQuestionDisabled()">Create Question</button>
          <mat-list>
          @for (question of questions; track question._id) {
            <mat-list-item>
              <h4 matLine>{{ question.text }}</h4>
              <p matLine>Options: {{ question.options.join(', ') }}</p>
              <button mat-icon-button color="warn" (click)="deleteQuestion(question._id)"><mat-icon>delete</mat-icon></button>
              <button mat-icon-button color="accent" (click)="startEditingQuestion(question)"><mat-icon>edit</mat-icon></button>
            </mat-list-item>
          }
          </mat-list>
          @if (editingQuestion) {
            <div>
              <mat-form-field><input matInput placeholder="Text" [(ngModel)]="editingQuestion.text"></mat-form-field>
              @for (option of editingQuestion.options; track $index; let i = $index) {
                <mat-form-field>
                  <input matInput placeholder="Option {{i + 1}}" [(ngModel)]="editingQuestion.options[i]">
                </mat-form-field>
              }
              <mat-form-field>
                <mat-select placeholder="Correct Answer" [(ngModel)]="editingQuestion.correctAnswer">
                  @for (option of editingQuestion.options; track $index; let i = $index) {
                    <mat-option [value]="i">{{ option }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <mat-form-field><input matInput type="number" placeholder="Marks" [(ngModel)]="editingQuestion.marks"></mat-form-field>
              <button mat-raised-button color="primary" (click)="updateQuestion()">Save</button>
              <button mat-button (click)="cancelEditing()">Cancel</button>
            </div>
          }
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Result Management -->
    <div class="card" fxFlex="30%">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Result Management</mat-card-title>
          <mat-form-field>
            <input matInput placeholder="Search by Student or Exam" [(ngModel)]="searchTerm" (input)="onSearchChange()">
          </mat-form-field>
        </mat-card-header>
        <mat-card-content>
          <mat-table [dataSource]="filteredResults.length ? filteredResults : results" class="mat-elevation-z8">
            <ng-container matColumnDef="student">
              <mat-header-cell *matHeaderCellDef> Student </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.studentId.username}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="exam">
              <mat-header-cell *matHeaderCellDef> Exam </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.examId.title}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="score">
              <mat-header-cell *matHeaderCellDef> Score </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.score}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="submittedAt">
              <mat-header-cell *matHeaderCellDef> Submitted At </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{ result.submittedAt ? (result.submittedAt) : '' }} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="answers">
              <mat-header-cell *matHeaderCellDef> Answers </mat-header-cell>
              <mat-cell *matCellDef="let result"> {{result.answers.length}} submitted </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="['student', 'exam', 'score', 'submittedAt', 'answers']"></mat-header-row>
            <mat-row *matRowDef="let row; columns: ['student', 'exam', 'score', 'submittedAt', 'answers'];"></mat-row>
          </mat-table>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
