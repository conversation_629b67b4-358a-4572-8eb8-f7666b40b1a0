import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule } from '@angular/forms';
import { ExamService } from '../../services/exam.service';
import { QuestionService } from '../../services/question.service';
import { ResultService } from '../../services/result.service';
import { AuthService } from '../../services/auth.service';
import { Exam } from '../../models/exam.model';
import { Question } from '../../models/question.model';
import { Result, Answer } from '../../models/result.model';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css'],
  standalone: true,
  imports: [
    MatToolbarModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatListModule,
    MatSelectModule,
    MatTableModule,
    MatIconModule,
    FlexLayoutModule,
    FormsModule
  ]
})
export class AdminDashboardComponent implements OnInit {
  isCreateQuestionDisabled(): boolean {
    // Disable if no exam is selected, question text is empty, or not all options are filled, or marks is not set
    if (!this.selectedExamId || !this.newQuestion.text || this.newQuestion.marks <= 0) return true;
    if (!this.newQuestion.options.every(opt => !!opt)) return true;
    return false;
  }
  exams: Exam[] = [];
  questions: Question[] = [];
  results: Result[] = [];
  users: User[] = [];
  filteredResults: Result[] = [];
  selectedExamId: string | null = null;
  newExam = { title: '', description: '', duration: 0 };
  newQuestion = { examId: '', text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 0 };
  editingExam: Exam | null = null;
  editingQuestion: Question | null = null;
  searchTerm: string = '';

  constructor(
    private examService: ExamService,
    private questionService: QuestionService,
    private resultService: ResultService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadExams();
    this.loadUsers();
    this.loadResults();
  }

  loadExams(): void {
    this.examService.getExams().subscribe(
      (data) => (this.exams = data),
      (error) => this.snackBar.open('Error loading exams', 'Close', { duration: 3000 })
    );
  }

  loadUsers(): void {
    this.authService.getUsers().subscribe(
      (data) => (this.users = data),
      (error) => this.snackBar.open('Error loading users', 'Close', { duration: 3000 })
    );
  }

  loadQuestions(examId: string): void {
    this.selectedExamId = examId;
    this.questionService.getQuestionsByExam(examId).subscribe(
      (data) => (this.questions = data),
      (error) => this.snackBar.open('Error loading questions', 'Close', { duration: 3000 })
    );
  }

  loadResults(): void {
    this.resultService.getAllResults().subscribe(
      (data) => {
        this.results = data;
        this.filterResults();
      },
      (error) => this.snackBar.open('Error loading results', 'Close', { duration: 3000 })
    );
  }

  filterResults(): void {
    this.filteredResults = this.results.filter(result =>
      result.studentId.username.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      result.examId.title.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  createExam(): void {
    this.examService.createExam(this.newExam).subscribe(
      (exam) => {
        this.exams.push(exam);
        this.newExam = { title: '', description: '', duration: 0 };
        this.snackBar.open('Exam created successfully', 'Close', { duration: 3000 });
      },
      (error) => this.snackBar.open('Error creating exam', 'Close', { duration: 3000 })
    );
  }

  updateExam(): void {
    if (this.editingExam) {
      this.examService.updateExam(this.editingExam._id, this.editingExam).subscribe(
        (updatedExam) => {
          const index = this.exams.findIndex(e => e._id === updatedExam._id);
          if (index !== -1) this.exams[index] = updatedExam;
          this.editingExam = null;
          this.snackBar.open('Exam updated successfully', 'Close', { duration: 3000 });
        },
        (error) => this.snackBar.open('Error updating exam', 'Close', { duration: 3000 })
      );
    }
  }

  deleteExam(examId: string): void {
    if (confirm('Are you sure you want to delete this exam?')) {
      this.examService.deleteExam(examId).subscribe(
        () => {
          this.exams = this.exams.filter(e => e._id !== examId);
          this.questions = [];
          this.selectedExamId = null;
          this.snackBar.open('Exam deleted successfully', 'Close', { duration: 3000 });
        },
        (error) => this.snackBar.open('Error deleting exam', 'Close', { duration: 3000 })
      );
    }
  }

  createQuestion(): void {
    if (this.selectedExamId) {
      this.newQuestion.examId = this.selectedExamId;
      this.questionService.createQuestion(this.newQuestion).subscribe(
        (question) => {
          this.questions.push(question);
          this.newQuestion = { examId: '', text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 0 };
          this.snackBar.open('Question created successfully', 'Close', { duration: 3000 });
        },
        (error) => this.snackBar.open('Error creating question', 'Close', { duration: 3000 })
      );
    }
  }

  updateQuestion(): void {
    if (this.editingQuestion) {
      this.questionService.updateQuestion(this.editingQuestion._id, this.editingQuestion).subscribe(
        (updatedQuestion) => {
          const index = this.questions.findIndex(q => q._id === updatedQuestion._id);
          if (index !== -1) this.questions[index] = updatedQuestion;
          this.editingQuestion = null;
          this.snackBar.open('Question updated successfully', 'Close', { duration: 3000 });
        },
        (error) => this.snackBar.open('Error updating question', 'Close', { duration: 3000 })
      );
    }
  }

  deleteQuestion(questionId: string): void {
    if (confirm('Are you sure you want to delete this question?')) {
      this.questionService.deleteQuestion(questionId).subscribe(
        () => {
          this.questions = this.questions.filter(q => q._id !== questionId);
          this.snackBar.open('Question deleted successfully', 'Close', { duration: 3000 });
        },
        (error) => this.snackBar.open('Error deleting question', 'Close', { duration: 3000 })
      );
    }
  }

  startEditingExam(exam: Exam): void {
    this.editingExam = { ...exam };
  }

  startEditingQuestion(question: Question): void {
    this.editingQuestion = { ...question };
  }

  cancelEditing(): void {
    this.editingExam = null;
    this.editingQuestion = null;
  }

  onSearchChange(): void {
    this.filterResults();
  }
}