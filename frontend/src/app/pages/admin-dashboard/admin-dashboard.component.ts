import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { CommonModule } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule } from '@angular/forms';
import { ExamService } from '../../services/exam.service';
import { QuestionService } from '../../services/question.service';
import { ResultService } from '../../services/result.service';
import { AuthService } from '../../services/auth.service';
import { Exam } from '../../models/exam.model';
import { Question } from '../../models/question.model';
import { Result } from '../../models/result.model';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatToolbarModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatListModule,
    MatSelectModule,
    MatTableModule,
    MatIconModule,
    MatMenuModule,
    MatBadgeModule,
    MatTooltipModule,
    MatDividerModule,
    MatProgressBarModule,
    FlexLayoutModule,
    FormsModule
  ]
})
export class AdminDashboardComponent implements OnInit {
  // Navigation and UI state
  activeTab: string = 'overview';
  loading = false;

  // Data arrays
  exams: Exam[] = [];
  questions: Question[] = [];
  results: Result[] = [];
  users: User[] = [];
  filteredResults: Result[] = [];

  // Search and filters
  selectedExamId: string | null = null;
  searchTerm: string = '';

  // Form models
  newExam = { title: '', description: '', duration: 60, isPublished: false };
  newQuestion = { examId: '', text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 1 };

  // Editing states
  editingExam: Exam | null = null;
  editingQuestion: Question | null = null;

  // Notifications
  notifications = [
    { id: 1, title: 'New student registered', icon: 'person_add', type: 'info', time: new Date() },
    { id: 2, title: 'Exam completed', icon: 'assignment_turned_in', type: 'success', time: new Date() },
    { id: 3, title: 'System maintenance scheduled', icon: 'warning', type: 'warning', time: new Date() }
  ];

  // Statistics
  stats = {
    totalExams: 0,
    totalQuestions: 0,
    totalUsers: 0,
    totalSubmissions: 0,
    averageScore: 0,
    completionRate: 0
  };

  isCreateQuestionDisabled(): boolean {
    // Disable if no exam is selected, question text is empty, or not all options are filled, or marks is not set
    if (!this.selectedExamId || !this.newQuestion.text || this.newQuestion.marks <= 0) return true;
    if (!this.newQuestion.options.every(opt => !!opt)) return true;
    return false;
  }

  constructor(
    private examService: ExamService,
    private questionService: QuestionService,
    private resultService: ResultService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadExams();
    this.loadUsers();
    this.loadResults();
  }

  loadExams(): void {
    this.examService.getExams().subscribe({
      next: (data) => (this.exams = data),
      error: () => this.snackBar.open('Error loading exams', 'Close', {
        duration: 3000,
        panelClass: ['snackbar-warn']
      })
    });
  }

  loadUsers(): void {
    this.authService.getUsers().subscribe({
      next: (data) => (this.users = data),
      error: () => this.snackBar.open('Error loading users', 'Close', {
        duration: 3000,
        panelClass: ['snackbar-warn']
      })
    });
  }

  loadQuestions(examId: string): void {
    this.selectedExamId = examId;
    this.questionService.getQuestionsByExam(examId).subscribe({
      next: (data) => (this.questions = data),
      error: () => this.snackBar.open('Error loading questions', 'Close', {
        duration: 3000,
        panelClass: ['snackbar-warn']
      })
    });
  }

  loadResults(): void {
    this.resultService.getAllResults().subscribe({
      next: (data) => {
        this.results = data;
        this.filterResults();
      },
      error: () => this.snackBar.open('Error loading results', 'Close', {
        duration: 3000,
        panelClass: ['snackbar-warn']
      })
    });
  }

  filterResults(): void {
    this.filteredResults = this.results.filter(result =>
      result.studentId.username.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      result.examId.title.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  createExam(): void {
    this.examService.createExam(this.newExam).subscribe({
      next: (exam) => {
        this.exams.push(exam);
        this.newExam = { title: '', description: '', duration: 60, isPublished: false };
        this.snackBar.open('Exam created successfully', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-success']
        });
      },
      error: () => this.snackBar.open('Error creating exam', 'Close', {
        duration: 3000,
        panelClass: ['snackbar-warn']
      })
    });
  }

  updateExam(): void {
    if (this.editingExam) {
      this.examService.updateExam(this.editingExam._id, this.editingExam).subscribe({
        next: (updatedExam) => {
          const index = this.exams.findIndex(e => e._id === updatedExam._id);
          if (index !== -1) this.exams[index] = updatedExam;
          this.editingExam = null;
          this.snackBar.open('Exam updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['snackbar-success']
          });
        },
        error: () => this.snackBar.open('Error updating exam', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-warn']
        })
      });
    }
  }

  deleteExam(examId: string): void {
    if (confirm('Are you sure you want to delete this exam?')) {
      this.examService.deleteExam(examId).subscribe({
        next: () => {
          this.exams = this.exams.filter(e => e._id !== examId);
          this.questions = [];
          this.selectedExamId = null;
          this.snackBar.open('Exam deleted successfully', 'Close', {
            duration: 3000,
            panelClass: ['snackbar-success']
          });
        },
        error: () => this.snackBar.open('Error deleting exam', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-warn']
        })
      });
    }
  }

  createQuestion(): void {
    if (this.selectedExamId) {
      this.newQuestion.examId = this.selectedExamId;
      this.questionService.createQuestion(this.newQuestion).subscribe({
        next: (question) => {
          this.questions.push(question);
          this.newQuestion = { examId: '', text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 0 };
          this.snackBar.open('Question created successfully', 'Close', {
            duration: 3000,
            panelClass: ['snackbar-success']
          });
        },
        error: () => this.snackBar.open('Error creating question', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-warn']
        })
      });
    }
  }

  updateQuestion(): void {
    if (this.editingQuestion) {
      this.questionService.updateQuestion(this.editingQuestion._id, this.editingQuestion).subscribe({
        next: (updatedQuestion) => {
          const index = this.questions.findIndex(q => q._id === updatedQuestion._id);
          if (index !== -1) this.questions[index] = updatedQuestion;
          this.editingQuestion = null;
          this.snackBar.open('Question updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['snackbar-success']
          });
        },
        error: () => this.snackBar.open('Error updating question', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-warn']
        })
      });
    }
  }

  deleteQuestion(questionId: string): void {
    if (confirm('Are you sure you want to delete this question?')) {
      this.questionService.deleteQuestion(questionId).subscribe({
        next: () => {
          this.questions = this.questions.filter(q => q._id !== questionId);
          this.snackBar.open('Question deleted successfully', 'Close', {
            duration: 3000,
            panelClass: ['snackbar-success']
          });
        },
        error: () => this.snackBar.open('Error deleting question', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-warn']
        })
      });
    }
  }

  startEditingExam(exam: Exam): void {
    this.editingExam = { ...exam };
  }

  startEditingQuestion(question: Question): void {
    this.editingQuestion = { ...question };
  }

  cancelEditing(): void {
    this.editingExam = null;
    this.editingQuestion = null;
  }

  onSearchChange(): void {
    this.filterResults();
  }

  // Navigation methods
  setActiveTab(tab: string): void {
    this.activeTab = tab;
    this.updateStats();
  }

  // Notification methods
  clearNotifications(): void {
    this.notifications = [];
  }

  // User management
  logout(): void {
    this.authService.logout();
    // Navigation will be handled by auth service
  }

  // Statistics calculation
  updateStats(): void {
    this.stats = {
      totalExams: this.exams.length,
      totalQuestions: this.questions.length,
      totalUsers: this.users.length,
      totalSubmissions: this.results.length,
      averageScore: this.calculateAverageScore(),
      completionRate: this.calculateCompletionRate()
    };
  }

  private calculateAverageScore(): number {
    if (this.results.length === 0) return 0;
    const totalScore = this.results.reduce((sum, result) => sum + result.score, 0);
    return Math.round((totalScore / this.results.length) * 100) / 100;
  }

  private calculateCompletionRate(): number {
    if (this.exams.length === 0 || this.users.length === 0) return 0;
    const totalPossibleSubmissions = this.exams.length * this.users.filter(u => u.role === 'student').length;
    return Math.round((this.results.length / totalPossibleSubmissions) * 100);
  }

  // Enhanced exam creation with validation
  isCreateExamDisabled(): boolean {
    return !this.newExam.title.trim() ||
           !this.newExam.description.trim() ||
           this.newExam.duration <= 0 ||
           this.loading;
  }

  // Enhanced question creation with better validation
  isCreateQuestionFormValid(): boolean {
    return this.selectedExamId !== null &&
           this.newQuestion.text.trim() !== '' &&
           this.newQuestion.options.every(opt => opt.trim() !== '') &&
           this.newQuestion.marks > 0 &&
           this.newQuestion.correctAnswer >= 0 &&
           this.newQuestion.correctAnswer < this.newQuestion.options.length;
  }

  // Form reset methods
  resetExamForm(): void {
    this.newExam = { title: '', description: '', duration: 60, isPublished: false };
  }

  resetQuestionForm(): void {
    this.newQuestion = { examId: '', text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 1 };
  }

  // Helper methods for displaying counts
  getQuestionCount(examId: string): number {
    // This would typically come from a service call, but for now return 0
    return 0;
  }

  getSubmissionCount(examId: string): number {
    return this.results.filter(result => result.examId._id === examId).length;
  }

  // Helper method for option labels
  getOptionLabel(index: number): string {
    return String.fromCharCode(65 + index);
  }

  // Helper method for score styling
  getScoreClass(score: number): string {
    if (score >= 80) return 'high-score';
    if (score >= 60) return 'medium-score';
    return 'low-score';
  }
}