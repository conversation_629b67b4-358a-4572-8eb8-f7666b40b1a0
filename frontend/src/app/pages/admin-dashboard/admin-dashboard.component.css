.dashboard-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.content {
  justify-content: space-around;
}

.card {
  transition: transform 0.3s ease;
}

.card:hover {
  transform: scale(1.02);
}

mat-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  background: #3f51b5;
  color: white;
  padding: 10px;
  border-radius: 12px 12px 0 0;
}

mat-card-title {
  font-size: 1.5em;
  font-weight: bold;
}

mat-form-field {
  width: 100%;
  margin: 10px 0;
}

mat-list-item {
  border-bottom: 1px solid #eee;
  padding: 10px;
}

mat-table {
  width: 100%;
  margin-top: 20px;
}

mat-row:hover {
  background: #f5f5f5;
}

button {
  margin: 5px;
}