/* Modern Admin Dashboard with Dark Theme */
.dashboard-container {
  padding: 0;
  background:
    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
  animation: backgroundShift 25s ease-in-out infinite;
}

.dashboard-container::after {
  content: '';
  position: absolute;
  top: 5%;
  right: 5%;
  width: 200px;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="rgba(255,255,255,0.08)"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>') no-repeat center;
  background-size: contain;
  opacity: 0.1;
  animation: float 12s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-40px) rotate(15deg); }
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.content {
  justify-content: space-around;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* Modern Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-radius: 0 0 24px 24px;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-top: 0.5rem;
  font-weight: 400;
}

.card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
}

mat-card {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
}

mat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

mat-card:hover::before {
  opacity: 1;
}

mat-card-header {
  background: rgba(63, 81, 181, 0.2);
  color: white;
  padding: 1.5rem;
  border-radius: 24px 24px 0 0;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

mat-card-title {
  font-size: 1.5em;
  font-weight: bold;
}

mat-form-field {
  width: 100%;
  margin: 10px 0;
}

mat-list-item {
  border-bottom: 1px solid #eee;
  padding: 10px;
}

mat-table {
  width: 100%;
  margin-top: 20px;
}

mat-row:hover {
  background: #f5f5f5;
}

button {
  margin: 5px;
}

/* Enhanced Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Enhanced Header Styles */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  gap: 2rem;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.brand-icon {
  font-size: 2.5rem;
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brand-title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.025em;
}

.brand-subtitle {
  margin: 0.25rem 0 0 0;
  opacity: 0.85;
  font-size: 0.875rem;
  font-weight: 400;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.nav-btn mat-icon {
  font-size: 1.125rem;
  width: 1.125rem;
  height: 1.125rem;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-btn.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
}

.action-btn {
  width: 2.75rem;
  height: 2.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.action-btn mat-icon {
  font-size: 1.25rem;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Main Content Styles */
.dashboard-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.tab-header {
  margin-bottom: 2rem;
  text-align: center;
}

.tab-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin: 0 0 0.5rem 0;
  font-size: 2.25rem;
  font-weight: 700;
  color: #2c3e50;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-description {
  margin: 0;
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 400;
}

.content-layout {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Enhanced Card Styles */
.create-card, .list-card, .edit-card, .selection-card, .results-card, .users-card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.create-card:hover, .list-card:hover, .edit-card:hover, .selection-card:hover, .results-card:hover, .users-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.card-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.card-avatar.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.card-avatar.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.card-avatar.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.card-avatar.info { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.card-avatar.accent { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
.card-avatar.error { background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%); }

/* Statistics Cards */
.stats-section {
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.stat-card.primary-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
.stat-card.success-card { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
.stat-card.warning-card { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; }
.stat-card.info-card { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #2c3e50; }
.stat-card.accent-card { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white; }
.stat-card.error-card { background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%); color: white; }

.stat-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
}

.stat-icon-wrapper {
  width: 4rem;
  height: 4rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 2rem;
}

.stat-details {
  flex: 1;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 0.25rem 0;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  margin: 0 0 0.5rem 0;
  opacity: 0.9;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.stat-trend.positive { color: #10b981; }
.stat-trend.negative { color: #ef4444; }
.stat-trend.neutral { color: #6b7280; }

.stat-trend mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.stat-progress {
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
}

.stat-progress mat-progress-bar {
  height: 4px;
}

/* Quick Actions */
.quick-actions-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1);
}

.action-card mat-card-content {
  text-align: center;
  padding: 2rem;
}

.action-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.action-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.action-card p {
  margin: 0;
  color: #64748b;
  font-size: 0.875rem;
}

/* Form Styles */
.exam-form, .question-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field {
  width: 100%;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  margin-top: 1rem;
}

.create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.create-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Options Section */
.options-section {
  margin: 1rem 0;
}

.section-subtitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.option-field {
  width: 100%;
}

/* Exams Grid */
.exams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.exam-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.exam-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.exam-card.editing {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.exam-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.exam-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.exam-duration {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.875rem;
}

.exam-description {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.exam-metadata {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
}

.metadata-item mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.exam-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-icon {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.empty-icon mat-icon {
  font-size: 2.5rem;
  color: #9ca3af;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.empty-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 2rem 0;
  max-width: 400px;
}

.empty-action {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
}

/* Table Styles */
.results-table-container {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.results-table {
  width: 100%;
  background: white;
}

.results-table mat-header-cell {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 1rem;
}

.results-table mat-cell {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.results-table mat-row:hover {
  background: #f8fafc;
}

.student-info, .exam-info, .score-info, .date-info, .answers-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.student-info mat-icon { color: #667eea; }
.exam-info mat-icon { color: #10b981; }
.score-info mat-icon { color: #f59e0b; }
.date-info mat-icon { color: #6b7280; }
.answers-info mat-icon { color: #8b5cf6; }

.score-value {
  font-weight: 600;
}

.high-score { color: #10b981; }
.medium-score { color: #f59e0b; }
.low-score { color: #ef4444; }

.search-section {
  margin-bottom: 2rem;
}

.search-field {
  max-width: 400px;
  width: 100%;
}

/* Users Grid */
.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.user-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  border-radius: 12px;
  color: white;
}

.user-avatar.admin {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar.student {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.detail-item mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.role-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.role-badge.admin {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.role-badge.student {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header-nav {
    gap: 0.25rem;
  }

  .nav-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .header-nav {
    order: 2;
    width: 100%;
    justify-content: space-around;
  }

  .nav-btn {
    flex: 1;
    justify-content: center;
    padding: 0.75rem 0.5rem;
  }

  .dashboard-main {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .exams-grid {
    grid-template-columns: 1fr;
  }

  .users-grid {
    grid-template-columns: 1fr;
  }
}

/* Questions Grid */
.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.question-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.question-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.question-card.editing {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.question-avatar {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.question-number {
  font-weight: 700;
  font-size: 1.125rem;
}

.question-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  line-height: 1.4;
}

.question-marks {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.875rem;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.option-item.correct {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
  color: #065f46;
}

.option-label {
  font-weight: 600;
  min-width: 1.5rem;
}

.option-text {
  flex: 1;
  font-size: 0.875rem;
}

.correct-icon {
  color: #10b981;
  font-size: 1.125rem;
}

.question-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Edit Question Form */
.edit-card {
  margin-top: 2rem;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
  border: 2px solid rgba(255, 193, 7, 0.3);
  animation: slideDown 0.3s ease-out;
}

.edit-card .card-avatar.warning {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.question-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-subtitle {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-subtitle::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.options-section {
  background: rgba(255, 255, 255, 0.5);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.option-field {
  width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1rem;
}

.form-field {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Responsive Design for Admin Dashboard */
@media (max-width: 1200px) {
  .dashboard-main {
    padding: 1.5rem 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  .questions-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .questions-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .options-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .brand-title {
    font-size: 1.25rem;
  }

  .brand-subtitle {
    display: none;
  }

  .dashboard-main {
    padding: 1rem 0.5rem;
  }

  .tab-content {
    padding: 1rem 0.75rem;
  }

  .tab-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .tab-description {
    text-align: center;
    font-size: 0.875rem;
  }

  .nav-btn span {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card mat-card-content {
    padding: 1rem !important;
  }

  .questions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .question-card {
    margin: 0;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .form-actions button {
    width: 100%;
  }

  .question-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .question-actions button {
    width: 100%;
  }

  .options-section {
    padding: 1rem;
  }

  .section-subtitle {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .brand-title {
    font-size: 1rem;
  }

  .dashboard-main {
    padding: 0.75rem 0.25rem;
  }

  .tab-content {
    padding: 0.75rem 0.5rem;
  }

  .tab-title {
    font-size: 1.25rem;
  }

  .tab-description {
    font-size: 0.75rem;
  }

  .stats-grid {
    gap: 0.75rem;
  }

  .stat-card mat-card-content {
    padding: 0.75rem !important;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .question-card {
    padding: 0;
  }

  .question-card mat-card-header {
    padding: 1rem 1rem 0.5rem;
  }

  .question-card mat-card-content {
    padding: 0.5rem 1rem;
  }

  .question-card mat-card-actions {
    padding: 0.5rem 1rem 1rem;
  }

  .question-title {
    font-size: 0.875rem;
    line-height: 1.3;
  }

  .question-marks {
    font-size: 0.75rem;
  }

  .option-item {
    padding: 0.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .option-label {
    font-weight: 700;
    color: #667eea;
  }

  .option-text {
    font-size: 0.75rem;
  }

  .edit-card {
    margin: 1rem 0;
  }

  .edit-card mat-card-content {
    padding: 1rem !important;
  }

  .question-form {
    gap: 1rem;
  }

  .options-section {
    padding: 0.75rem;
  }

  .form-field {
    margin-bottom: 0.5rem;
  }

  .form-actions {
    gap: 0.5rem;
    padding-top: 0.75rem;
  }
}

/* Enhanced Floating Action Button */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.fab-main {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  border: none;
  box-shadow: var(--shadow-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  animation: fabPulse 3s ease-in-out infinite;
}

.fab-main:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-2xl);
}

@keyframes fabPulse {
  0%, 100% { box-shadow: var(--shadow-xl); }
  50% { box-shadow: var(--shadow-2xl), 0 0 30px rgba(99, 102, 241, 0.4); }
}

/* Enhanced Card Animations */
.dashboard-card {
  transition: all var(--transition-normal);
  animation: cardSlideIn 0.6s ease-out;
}

.dashboard-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered Animation for Cards */
.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }

/* Enhanced Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Success Animation */
.success-animation {
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Error Animation */
.error-animation {
  animation: errorShake 0.6s ease-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Notification Toast Animations */
.notification-toast {
  animation: toastSlideIn 0.3s ease-out;
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Progress Bar Animation */
.progress-bar-animated {
  animation: progressGrow 1s ease-out;
}

@keyframes progressGrow {
  from { width: 0; }
  to { width: var(--final-width); }
}

/* Mobile FAB Adjustments */
@media (max-width: 768px) {
  .fab-container {
    bottom: 1rem;
    right: 1rem;
  }

  .fab-main {
    width: 48px;
    height: 48px;
  }
}