import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { QuestionService } from '../../services/question.service';
import { Question } from '../../models/question.model';

@Component({
  selector: 'app-question-manage',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatTableModule, MatIconModule, FormsModule],
  templateUrl: './question-manage.component.html',
  styleUrl: './question-manage.component.css'
})
export class QuestionManageComponent implements OnInit {
getOption(_t37: number): any {
throw new Error('Method not implemented.');
}
  questions: Question[] = [];
  newQuestion: Partial<Question> = { text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 1, examId: '' };
  editingQuestion: Question | null = null;
  loading = false;

  constructor(private questionService: QuestionService) {}

  ngOnInit() {
    // Optionally, load questions for a default exam or expose a method to set examId
  }

  loadQuestions(examId: string) {
    this.loading = true;
    this.questionService.getQuestionsByExam(examId).subscribe({
      next: (data) => { this.questions = data; this.loading = false; },
      error: () => { this.loading = false; }
    });
  }

  createQuestion() {
    if (!this.newQuestion.text || !this.newQuestion.options?.every(opt => opt) || this.newQuestion.marks! <= 0 || !this.newQuestion.examId) return;
    this.questionService.createQuestion({
      examId: this.newQuestion.examId!,
      text: this.newQuestion.text!,
      options: this.newQuestion.options!,
      correctAnswer: this.newQuestion.correctAnswer!,
      marks: this.newQuestion.marks!
    }).subscribe(() => {
      this.loadQuestions(this.newQuestion.examId!);
      this.newQuestion = { text: '', options: ['', '', '', ''], correctAnswer: 0, marks: 1, examId: this.newQuestion.examId };
    });
  }

  startEdit(question: Question) {
    this.editingQuestion = { ...question };
  }

  updateQuestion() {
    if (!this.editingQuestion) return;
    this.questionService.updateQuestion(this.editingQuestion._id, this.editingQuestion).subscribe(() => {
      this.loadQuestions(this.editingQuestion!.examId);
      this.editingQuestion = null;
    });
  }

  deleteQuestion(id: string) {
    if (!this.newQuestion.examId) return;
    this.questionService.deleteQuestion(id).subscribe(() => this.loadQuestions(this.newQuestion.examId!));
  }

  cancelEdit() {
    this.editingQuestion = null;
  }
}
