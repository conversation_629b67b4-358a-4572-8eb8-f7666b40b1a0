.login-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f0ff 0%, #b6d0f7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-center {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
}
.login-card {
  width: 350px;
  padding: 28px 24px 24px 24px;
  border-radius: 18px;
  box-shadow: 0 8px 24px rgba(33,150,243,0.10), 0 1.5px 4px rgba(33,150,243,0.10);
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
}
mat-card-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1976d2;
  text-align: center;
}
.full-width {
  width: 100%;
}
.login-btn {
  width: 100%;
  margin-top: 18px;
  font-size: 1.1rem;
  font-weight: 600;
  background: linear-gradient(90deg, #1976d2 0%, #2196f3 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(33,150,243,0.15);
  transition: box-shadow 0.2s, transform 0.2s;
}
.login-btn:hover {
  box-shadow: 0 4px 16px rgba(33,150,243,0.25);
  transform: translateY(-2px) scale(1.03);
}
.snackbar-success {
  background: #43a047 !important;
  color: #fff !important;
}
.snackbar-warn {
  background: #e53935 !important;
  color: #fff !important;
}