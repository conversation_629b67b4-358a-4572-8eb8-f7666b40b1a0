
import { Component } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  loginForm: FormGroup;
  loading = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar,
    private fb: FormBuilder
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  get email() { return this.loginForm.get('email'); }
  get password() { return this.loginForm.get('password'); }

  onSubmit(): void {
    if (this.loginForm.invalid) {
      this.snackBar.open('Please enter valid credentials', 'Close', { duration: 2500, panelClass: 'snackbar-warn' });
      return;
    }
    this.loading = true;
    const credentials = this.loginForm.value;
    this.authService.login(credentials).subscribe(
      (response) => {
        this.authService.setToken(response.token);
        // Decode token to get user role
        const payload = JSON.parse(atob(response.token.split('.')[1]));
        const role = payload.user.role;
        this.snackBar.open('Login successful', 'Close', { duration: 2000, panelClass: 'snackbar-success' });
        setTimeout(() => {
          if (role === 'admin') {
            this.router.navigate(['/admin-dashboard']);
          } else {
            this.router.navigate(['/student-dashboard']);
          }
        }, 500);
      },
      (error) => {
        this.snackBar.open('Login failed: ' + (error.error?.message || 'Invalid credentials'), 'Close', { duration: 3000, panelClass: 'snackbar-warn' });
        this.loading = false;
      },
      () => {
        this.loading = false;
      }
    );
  }
}
