<div class="login-bg">
  <div class="login-center">
    <mat-card class="login-card animate__animated animate__fadeInDown">
      <mat-card-header>
        <mat-card-title>Login</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" autocomplete="off">
          <mat-form-field appearance="outline" class="full-width">
            <input matInput placeholder="Email" formControlName="email" type="email" required>
            <mat-error *ngIf="email?.hasError('required')">Email is required</mat-error>
            <mat-error *ngIf="email?.hasError('email')">Enter a valid email</mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="full-width">
            <input matInput placeholder="Password" formControlName="password" type="password" required>
            <mat-error *ngIf="password?.hasError('required')">Password is required</mat-error>
            <mat-error *ngIf="password?.hasError('minlength')">Password must be at least 6 characters</mat-error>
          </mat-form-field>
          <button mat-raised-button color="primary" class="login-btn" type="submit" [disabled]="loginForm.invalid || loading">
            <span *ngIf="!loading">Login</span>
            <mat-progress-spinner *ngIf="loading" diameter="22" mode="indeterminate" color="accent"></mat-progress-spinner>
          </button>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>
