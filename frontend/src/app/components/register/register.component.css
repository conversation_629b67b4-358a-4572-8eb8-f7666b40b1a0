mat-card {
  width: 350px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12), 0 1.5px 4px rgba(0,0,0,0.12);
  border-radius: 18px;
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
}
mat-card-title {
  font-size: 2rem;
  font-weight: 700;
  color: #3f51b5;
  text-align: center;
}
.full-width {
  width: 100%;
}
button[mat-raised-button] {
  width: 100%;
  margin-top: 18px;
  font-size: 1.1rem;
  font-weight: 600;
  background: linear-gradient(90deg, #3f51b5 0%, #2196f3 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(33,150,243,0.15);
}
