<div fxLayout="column" fxLayoutAlign="center center" style="height: 100vh;">
  <mat-card>
    <mat-card-header>
      <mat-card-title>Register</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline" class="full-width">
          <input matInput placeholder="Username" [(ngModel)]="user.username" name="username" required>
        </mat-form-field>
        <mat-form-field appearance="outline" class="full-width">
          <input matInput placeholder="Email" [(ngModel)]="user.email" name="email" required type="email">
        </mat-form-field>
        <mat-form-field appearance="outline" class="full-width">
          <input matInput type="password" placeholder="Password" [(ngModel)]="user.password" name="password" required>
        </mat-form-field>
        <button mat-raised-button color="primary" type="submit" [disabled]="!user.username || !user.email || !user.password">Register</button>
      </form>
    </mat-card-content>
  </mat-card>
</div>
