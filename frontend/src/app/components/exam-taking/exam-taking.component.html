<div *ngIf="exam && questions.length && !loading" class="exam-taking-root">
  <mat-card>
    <mat-card-title>{{ exam.title }}</mat-card-title>
    <mat-card-content>
      <form (ngSubmit)="onSubmit()">
        <div *ngFor="let question of questions; let i = index">
          <mat-card class="question-card animate__animated animate__fadeInUp">
            <mat-card-title>Q{{ i + 1 }}: {{ question.text }}</mat-card-title>
            <mat-radio-group [(ngModel)]="answers[i]" [name]="'answer' + i">
              <mat-radio-button *ngFor="let option of question.options; let j = index" [value]="j">
                {{ option }}
              </mat-radio-button>
            </mat-radio-group>
          </mat-card>
        </div>
        <button mat-raised-button color="accent" type="submit">Submit Exam</button>
        <button mat-button type="button" (click)="onCancel()">Cancel</button>
      </form>
    </mat-card-content>
  </mat-card>
</div>
<div *ngIf="loading" class="loading-spinner">
  <mat-progress-spinner diameter="40" color="primary" mode="indeterminate"></mat-progress-spinner>
</div>
