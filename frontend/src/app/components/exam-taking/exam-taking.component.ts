import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Question } from '../../models/question.model';
import { Exam } from '../../models/exam.model';

@Component({
  selector: 'app-exam-taking',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatRadioModule, FormsModule, MatProgressSpinnerModule],
  templateUrl: './exam-taking.component.html',
  styleUrl: './exam-taking.component.css'
})
export class ExamTakingComponent {
  @Input() exam: Exam | null = null;
  @Input() questions: Question[] = [];
  @Input() answers: number[] = [];
  @Input() loading: boolean = false;
  @Output() submitExam = new EventEmitter<void>();
  @Output() cancelExam = new EventEmitter<void>();

  onSubmit() {
    this.submitExam.emit();
  }
  onCancel() {
    this.cancelExam.emit();
  }
}
