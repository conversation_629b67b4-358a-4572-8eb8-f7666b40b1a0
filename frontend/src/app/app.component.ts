import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-root',
  template: `
    <mat-toolbar color="primary">
      <span>Exam System</span>
    </mat-toolbar>
    <router-outlet></router-outlet>
  `,
  standalone: true,
  imports: [RouterOutlet, MatToolbarModule]
})
export class AppComponent {
  title = 'frontend';
}
