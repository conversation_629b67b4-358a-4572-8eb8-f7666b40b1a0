import { HttpInterceptorFn } from '@angular/common/http';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  // Get the token from localStorage
  const token = localStorage.getItem('token');

  // If token exists, clone the request and add the x-auth-token header
  if (token) {
    const authReq = req.clone({
      headers: req.headers.set('x-auth-token', token)
    });
    return next(authReq);
  }

  // If no token, proceed with the original request
  return next(req);
};
